# 肩盂假体基座螺钉植入路径规划系统

[![Python](https://img.shields.io/badge/Python-3.8+-blue.svg)](https://www.python.org/) [![SimpleITK](https://img.shields.io/badge/SimpleITK-2.0+-green.svg)](https://simpleitk.org/) [![NumPy](https://img.shields.io/badge/NumPy-1.20+-red.svg)](https://numpy.org/)


## 📁 项目结构

```
ScrewPlanning/
├── src/                        # 源代码
│   ├── core/                   # 核心算法模块
│   │   ├── bone_density.py     # 骨密度计算
│   │   ├── cone_space.py       # 锥形空间生成
│   │   ├── path_planning.py    # 路径规划算法
│   │   └── optimization.py     # 路径优化
│   ├── utils/                  # 工具函数
│   │   ├── geometry.py         # 几何计算
│   │   ├── image_processing.py # 图像处理
│   │   └── io_utils.py         # 文件输入输出
│   └── main.py                 # 命令行工具入口
├── docs/                       # 文档
├── tests/                      # 测试文件
├── examples/                   # 示例数据
├── run_screw_planning.py       # 启动脚本
└── README.md                   # 项目说明
```

## 📖 参考文献

Li, H., Xu, J., Zhang, D., He, Y., & Chen, X. (2022). Automatic surgical planning based on bone density assessment and path integral in cone space for reverse shoulder arthroplasty. *International Journal of Computer Assisted Radiology and Surgery*, 17(8), 1535-1546.